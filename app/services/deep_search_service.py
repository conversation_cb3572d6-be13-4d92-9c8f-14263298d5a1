import asyncio
import json
import logging
from typing import Any, Dict, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from app.services.generic_chat_service import save_chat, update_chat_history
from app.utils.chat_faqs_utils import get_answer_based_on_faqs
from app.utils.deep_search import (
    analyze_and_generate_sub_questions,
    analyze_and_generate_sub_questions_v3,
    generate_sub_question_titles,
    generate_sub_question_titles_v3,
    generate_synthesis_title,
    research_sub_questions_parallel,
    research_sub_questions_parallel_v3,
    synthesize_findings,
)
from app.utils.validate_user_query_request import validate_user_question

logger = logging.getLogger(__name__)


async def deep_search_stream_handler(
    db: AsyncSession,
    chat_id: str,
    account_id: str,
    question: str,
    chat_history: List[Dict[str, Any]],
    reply: Optional[str] = None,
    track: str = "",
    parent: str = "",
):
    """
    Stream handler for deep search functionality with dynamic titles and parallel processing.

    Args:
        db: Database session
        chat_id: Chat ID
        account_id: Account ID
        question: User question
        chat_history: Chat history
        reply: Optional reply
        track: Track ID
        parent: Parent chat ID

    Yields:
        Streaming response chunks
    """
    history_key = f"chat_history_{chat_id}"

    data_to_save = dict()
    try:
        # Step 1: Initial loading state
        resp = json.dumps(
            {"loading_message": "We're analyzing your question", "type": "loading"}
        )
        yield f"data: {resp}\n\n"

        # Step 2: Generate and research sub-questions
        logger.info("Starting deep search analysis")
        analysis, sub_questions = await analyze_and_generate_sub_questions(
            question, chat_history
        )
        # Replace ```html from analysis
        analysis = analysis.replace("```html", "").replace("```", "")

        # Yield the analysis immediately after getting it
        # print("Analysis: started ==========>")
        resp = json.dumps(
            {
                "loading_answer": analysis,
                "type": "final_answer",
            }
        )
        yield f"data: {resp}\n\n"

        # Step 3: Generate dynamic titles for sub-questions
        logger.info("Generating dynamic titles for sub-questions")
        sub_question_titles = await generate_sub_question_titles(
            question, sub_questions
        )

        # Step 4: Research all sub-questions in parallel
        logger.info("Starting parallel research of sub-questions")
        research_results = await research_sub_questions_parallel(sub_questions)

        # Step 5: Stream each sub-question with loading → answer → citations cycle
        data_to_save["research"] = []
        for i, res in enumerate(research_results):
            # Stream loading message for this sub-question
            title = (
                sub_question_titles[i]
                if i < len(sub_question_titles)
                else f"Analysis {i+1}"
            )
            idx_key = str(i + 1)  # Start from 1 for better UX

            resp = json.dumps(
                {"loading_message": title, "type": "loading", "idx_key": idx_key}
            )
            yield f"data: {resp}\n\n"

            # Small delay to show loading state
            await asyncio.sleep(5.0)

            # Stream the answer for this sub-question
            resp = json.dumps(
                {
                    "loading_answer": res["answer"],
                    "type": "final_answer",
                    "idx_key": idx_key,
                }
            )
            yield f"data: {resp}\n\n"
            chat_to_dave = {"answer": res["answer"], "title": title, "index": i}
            # Stream citations if they exist
            if "citations" in res and res["citations"]:
                resp = json.dumps(
                    {
                        "citation_message": res["citations"],
                        "type": "links",
                        "idx_key": idx_key,
                    }
                )
                chat_to_dave["citations"] = res["citations"]
                yield f"data: {resp}\n\n"
            data_to_save["research"].append(chat_to_dave)

        # Step 6: Generate synthesis title and show loading
        logger.info("Generating synthesis title")
        synthesis_title = await generate_synthesis_title(question, sub_questions)

        # Calculate idx_key for synthesis (continues from sub-questions)
        synthesis_idx_key = str(len(research_results) + 1)

        resp = json.dumps(
            {
                "loading_message": synthesis_title,
                "type": "loading",
                "idx_key": synthesis_idx_key,
            }
        )
        yield f"data: {resp}\n\n"

        # Step 7: Generate and stream final synthesis
        logger.info("Generating final synthesis")
        synthesis_result = await synthesize_findings(question, research_results)

        resp = json.dumps(
            {
                "loading_answer": synthesis_result,
                "type": "final_answer",
                "idx_key": synthesis_idx_key,
            }
        )
        yield f"data: {resp}\n\n"

        # Add synthesis to research array as the final item
        synthesis_data = {
            "answer": synthesis_result,
            "title": synthesis_title,
            "index": len(research_results),  # Continue indexing from sub-questions
            "type": "synthesis",  # Mark this as synthesis for identification
        }
        data_to_save["research"].append(synthesis_data)
        # Step 8: Prepare complete HTML for database storage
        # Format analysis as HTML
        analysis_html = f'<div class="analysis">{analysis}</div>'

        # Format sub-questions as HTML
        sub_q_html = """
        <div class="sub-questions">
          <h2>Breaking down into sub-questions:</h2>
          <ol>
        """
        for q in sub_questions:
            sub_q_html += f"<li>{q}</li>\n"
        sub_q_html += """
          </ol>
        </div>
        """

        # Format research findings as HTML
        research_html = """
        <div class="research-findings">
          <h2>Research Findings:</h2>
        """
        for i, result_item in enumerate(research_results):
            research_html += f"""
            <div class="research-item">
              <h3>Sub-question {i+1}: {result_item['question']}</h3>
              <div class="research-answer">
                {result_item['answer']}
              </div>
            </div>
            """
        research_html += "</div>"

        # Wrap the final synthesis in a div if it's not already wrapped
        if not synthesis_result.strip().startswith("<div"):
            final_html = f'<div class="comprehensive-answer">{synthesis_result}</div>'
        else:
            final_html = synthesis_result

        # Combine all HTML sections
        complete_html = f"""
        <div class="deep-search-result-container">
            {analysis_html}
            {sub_q_html}
            {research_html}
            {final_html}
        </div>
        """

        # Save to chat history
        update_chat_history(history_key, chat_history, question, complete_html)

        # Save to database
        chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=complete_html,
            reply=reply,
            deep_search=True,
            **data_to_save,
        )

        # Send final response with database info
        result = {
            "answer": final_html,
            "chat_id": chat_data.get("chat_id"),
            "id": chat_data.get("id"),
            "is_html": True,
        }

        resp = json.dumps(
            {
                "detail": result,
                "type": "final",
            }
        )
        yield f"data: {resp}\n\n"

        logger.info("Stream completed successfully")
        return

    except Exception as e:
        logger.error(f"Error in analysis pipeline: {str(e)}", exc_info=True)

        # Format error message as HTML
        error_html = f"""
        <div class="error-message">
            <h2>Error in Deep Search</h2>
            <p>I encountered an error while performing deep search:</p>
            <div class="error-details">
                <p><strong>Error:</strong> {str(e)}</p>
            </div>
            <p>Please try again or rephrase your question.</p>
        </div>
        """

        # Save error message
        chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=error_html,
            reply=reply,
            deep_search=True,
            **data_to_save,
        )

        result = {
            "answer": error_html,
            "chat_id": chat_data.get("chat_id"),
            "id": chat_data.get("id"),
            "error": str(e),
            "is_html": True,
        }

        resp = json.dumps(
            {
                "detail": result,
                "type": "final",
            }
        )
        yield f"data: {resp}\n\n"


async def deep_search_stream_handler_v3(
    db: AsyncSession,
    chat_id: str,
    account_id: str,
    question: str,
    chat_history: List[Dict[str, Any]],
    reply: Optional[str] = None,
    track: str = "",
    parent: str = "",
):
    """
    Stream handler for deep search functionality with dynamic titles and parallel processing.

    Args:
        db: Database session
        chat_id: Chat ID
        account_id: Account ID
        question: User question
        chat_history: Chat history
        reply: Optional reply
        track: Track ID
        parent: Parent chat ID

    Yields:
        Streaming response chunks
    """
    history_key = f"chat_history_{chat_id}"

    # Step 1: Initial loading state
    resp = json.dumps(
        {"loading_message": "We're analyzing your question", "type": "loading"}
    )
    yield f"data: {resp}\n\n"

    resp = await get_answer_based_on_faqs(question=question, type="deepsearch")
    if resp:
        print(resp)
        if (
            "<p>Could you please clarify what you’re asking about? Provide some more context.</p>"
            not in resp
        ):
            last_data = json.loads(resp[-1])
            complete_html = last_data["detail"]["database"]
            print(complete_html)
            reasearch_data = last_data["detail"]["reasearch_data"]

            # Save to chat history
            update_chat_history(history_key, chat_history, question, complete_html)

            # Save to database
            save_chat_data = await save_chat(
                chat_id,
                parent,
                question=question,
                answer=complete_html,
                reply=reply,
                deep_search=True,
                **reasearch_data,
            )

            for data in resp[1:-1]:
                yield f"data: {data}\n\n"

            last_data["detail"]["chat_id"] = save_chat_data.get("chat_id")
            last_data["detail"]["id"] = save_chat_data.get("id")
            del last_data["detail"]["database"]
            del last_data["detail"]["reasearch_data"]
            yield f"data: {last_data}\n\n"

            return

    response = validate_user_question(question, chat_id, chat_history)

    updated_question = question
    if response["is_realtime"]:
        updated_question = response["new_question"]
    else:
        resp = json.dumps(
            {
                "loading_answer": response["answer"],
                "type": "final_answer",
            }
        )
        yield f"data: {resp}\n\n"
        # Save to chat history
        update_chat_history(history_key, chat_history, question, response["answer"])

        # Save to database
        save_chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=response["answer"],
            reply=reply,
            deep_search=False,
        )
        result = {
            "answer": "",
            "chat_id": save_chat_data.get("chat_id"),
            "id": save_chat_data.get("id"),
        }
        resp = json.dumps(
            {
                "detail": result,
                "type": "final",
            }
        )
        yield f"data: {resp}\n\n"

        resp = json.dumps(
            {"loading_message": "We're analyzing your question", "type": "loading"}
        )

        yield f"data: {resp}\n\n"

        return

    data_to_save = dict()
    try:
        # Step 2: Generate and research sub-questions
        logger.info("Starting deep search analysis")
        analysis, sub_questions = await analyze_and_generate_sub_questions_v3(
            updated_question, chat_history
        )
        # Yield the analysis immediately after getting it
        # print("Analysis: started ==========>")

        resp = json.dumps(
            {
                "loading_answer": analysis.replace("```html", "")
                .replace("```", "")
                .strip(),
                "type": "final_answer",
            }
        )
        yield f"data: {resp}\n\n"

        # Step 3: Generate dynamic titles for sub-questions
        logger.info("Generating dynamic titles for sub-questions")
        sub_question_titles = await generate_sub_question_titles_v3(
            updated_question, sub_questions
        )
        # Step 4: Research all sub-questions in parallel
        logger.info("Starting parallel research of sub-questions")
        research_results = await research_sub_questions_parallel_v3(sub_questions)
        # Step 5: Stream each sub-question with loading → answer → citations cycle
        data_to_save["research"] = []
        for i, res in enumerate(research_results):
            # Stream loading message for this sub-question
            title = (
                sub_question_titles[i]
                if i < len(sub_question_titles)
                else f"Analysis {i+1}"
            )
            idx_key = str(i + 1)  # Start from 1 for better UX

            resp = json.dumps(
                {"loading_message": title, "type": "loading", "idx_key": idx_key}
            )
            yield f"data: {resp}\n\n"

            # Small delay to show loading state
            await asyncio.sleep(5.0)

            # Stream the answer for this sub-question
            resp = json.dumps(
                {
                    "loading_answer": res["answer"]
                    .replace("```html", "")
                    .replace("```", "")
                    .strip(),
                    "type": "final_answer",
                    "idx_key": idx_key,
                }
            )
            yield f"data: {resp}\n\n"
            chat_to_dave = {"answer": res["answer"], "title": title, "index": i}
            # Stream citations if they exist
            if "citations" in res and res["citations"]:
                resp = json.dumps(
                    {
                        "citation_message": res["citations"],
                        "type": "links",
                        "idx_key": idx_key,
                    }
                )
                chat_to_dave["citations"] = res["citations"]
                yield f"data: {resp}\n\n"
            data_to_save["research"].append(chat_to_dave)

        # Step 6: Generate synthesis title and show loading
        logger.info("Generating synthesis title")
        synthesis_title = await generate_sub_question_titles_v3(
            updated_question, sub_questions
        )
        # Calculate idx_key for synthesis (continues from sub-questions)
        synthesis_idx_key = str(len(research_results) + 1)

        resp = json.dumps(
            {
                "loading_message": synthesis_title,
                "type": "loading",
                "idx_key": synthesis_idx_key,
            }
        )
        yield f"data: {resp}\n\n"

        # Step 7: Generate and stream final synthesis
        logger.info("Generating final synthesis")
        synthesis_result = await synthesize_findings(updated_question, research_results)

        resp = json.dumps(
            {
                "loading_answer": synthesis_result.replace("```html", "")
                .replace("```", "")
                .strip(),
                "type": "final_answer",
                "idx_key": synthesis_idx_key,
            }
        )

        yield f"data: {resp}\n\n"

        # Add synthesis to research array as the final item
        synthesis_data = {
            "answer": synthesis_result,
            "title": synthesis_title,
            "index": len(research_results),  # Continue indexing from sub-questions
            "type": "synthesis",  # Mark this as synthesis for identification
        }
        data_to_save["research"].append(synthesis_data)
        # Step 8: Prepare complete HTML for database storage
        # Format analysis as HTML
        analysis_html = f'<div class="analysis">{analysis}</div>'

        # Format sub-questions as HTML
        sub_q_html = """
        <div class="sub-questions">
          <h2>Breaking down into sub-questions:</h2>
          <ol>
        """
        for q in sub_questions:
            sub_q_html += f"<li>{q}</li>\n"
        sub_q_html += """
          </ol>
        </div>
        """

        # Format research findings as HTML
        research_html = """
        <div class="research-findings">
          <h2>Research Findings:</h2>
        """
        for i, result_item in enumerate(research_results):
            research_html += f"""
            <div class="research-item">
              <h3>Sub-question {i+1}: {result_item['question']}</h3>
              <div class="research-answer">
                {result_item['answer']}
              </div>
            </div>
            """
        research_html += "</div>"

        # Wrap the final synthesis in a div if it's not already wrapped
        if not synthesis_result.strip().startswith("<div"):
            final_html = f'<div class="comprehensive-answer">{synthesis_result}</div>'
        else:
            final_html = synthesis_result

        # Combine all HTML sections
        complete_html = f"""
        <div class="deep-search-result-container">
            {analysis_html}
            {sub_q_html}
            {research_html}
            {final_html}
        </div>
        """

        # Save to chat history
        update_chat_history(history_key, chat_history, question, complete_html)

        # Save to database
        chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=complete_html,
            reply=reply,
            deep_search=True,
            **data_to_save,
        )

        # Send final response with database info
        result = {
            "answer": final_html,
            "chat_id": chat_data.get("chat_id"),
            "id": chat_data.get("id"),
            "is_html": True,
        }

        resp = json.dumps(
            {
                "detail": result,
                "type": "final",
            }
        )
        yield f"data: {resp}\n\n"
        logger.info("Stream completed successfully")
        return

    except Exception as e:
        logger.error(f"Error in analysis pipeline: {str(e)}", exc_info=True)

        # Format error message as HTML
        error_html = f"""
        <div class="error-message">
            <h2>Error in Deep Search</h2>
            <p>I encountered an error while performing deep search:</p>
            <div class="error-details">
                <p><strong>Error:</strong> {str(e)}</p>
            </div>
            <p>Please try again or rephrase your question.</p>
        </div>
        """

        # Save error message
        chat_data = await save_chat(
            chat_id,
            parent,
            question=question,
            answer=error_html,
            reply=reply,
            deep_search=True,
            **data_to_save,
        )

        result = {
            "answer": error_html,
            "chat_id": chat_data.get("chat_id"),
            "id": chat_data.get("id"),
            "error": str(e),
            "is_html": True,
        }
        resp = json.dumps(
            {
                "detail": result,
                "type": "final",
            }
        )
        yield f"data: {resp}\n\n"
