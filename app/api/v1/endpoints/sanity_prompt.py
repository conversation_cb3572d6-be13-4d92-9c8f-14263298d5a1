import os

import requests
from dotenv import load_dotenv
from fastapi import APIRouter

from app.schemas.sanity_schemas import CategoryResponse, PromptResponse

# Load environment variables
load_dotenv()

# Sanity API URL (replace with your actual project ID and dataset)
SANITY_API_URL = os.getenv("SANITY_API_URL")

# Define the GROQ query
QUERY = """
*[_type == "ai_prompts"]{
  title,
  slug,
  mode,
  parent->{
    _id,
    title
  },
  _createdAt,
  _updatedAt
}
"""


router = APIRouter()


def fetch_prompts():
    params = {"query": QUERY}
    response = requests.get(SANITY_API_URL, params=params, timeout=180)
    if response.status_code == 200:
        return response.json().get(
            "result", []
        )  # Get 'result' key or empty list if not found
    else:
        return []


@router.get("/prompts", response_model=PromptResponse)
async def get_prompts():
    prompts = fetch_prompts()
    return {"result": prompts}


# Define the GROQ query
CATEGORY_QUERY = """
*[_type == "ai_categories"]{
  title,
  slug,
  parent->{
    _id,
    title
  },
  description,
  image,
  _createdAt,
  _updatedAt
}
"""


def fetch_categories():
    params = {"query": CATEGORY_QUERY}
    response = requests.get(SANITY_API_URL, params=params)

    if response.status_code == 200:
        return response.json().get(
            "result", []
        )  # Get 'result' key or empty list if not found
    else:
        print(f"Error fetching categories: {response.status_code}")
        return []


@router.get("/categories", response_model=CategoryResponse)
async def get_categories():
    categories = fetch_categories()
    return {"result": categories}
