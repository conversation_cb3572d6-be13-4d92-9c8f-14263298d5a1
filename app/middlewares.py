import os
import time
import traceback
from datetime import datetime, timedelta, timezone
from functools import wraps
from typing import Optional

from fastapi import HTT<PERSON><PERSON>x<PERSON>, Request, UploadFile
from fastapi.responses import (
    JSONResponse,
    PlainTextResponse,
    Response,
    StreamingResponse,
)
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse, Response, StreamingResponse

from app.core.config import settings
from app.services.mail_service import send_alert_mail
from app.utils import cache
from app.utils.jwt_utils import decode_jwt
from app.utils.logger import chatbot_logger as logger

VERSION2_URLS = [
    "/api/v1/generic_bot/generic-v2",
    "/api/v1/generic_bot/analyze-chart-web-v2",
    "/api/v2/agents/chat",
    "/api/v3/agents_v3/chat_v3",
    "/api/v3/watchlist_v3/watchlist_symbol_v3",
    "/api/v3/generic_bot_v3/generic-v3",
    "/api/v3/drawing_v3/drawing-v3",
]


class JWTAuthMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time.time()

        # Skip logging for specific paths
        if request.url.path in [
            "/",
            "/test-apis",
            "/redoc",
            "/openapi.json",
            "/favicon.ico",
            "/api/v1/agents/chat-with-public-agent",
        ]:
            return await call_next(request)

        # Extract Authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            logger.warning(
                f"Unauthorized access attempt: {request.method} {request.url}"
            )
            return JSONResponse(
                status_code=401, content="Authorization header missing or invalid"
            )

        token = auth_header.split(" ")[1]
        try:
            payload = decode_jwt(token)
        except HTTPException as e:
            logger.error(f"JWT Error: {str(e)}")
            return JSONResponse(status_code=e.status_code, content={"detail": e.detail})
        except Exception as e:
            logger.error(f"Internal Server Error: {traceback.format_exc()}")
            send_alert_mail(traceback.format_exc())
            return JSONResponse(
                status_code=500, content={"detail": f"Internal server error: {str(e)}"}
            )

        request.state.customer_id = payload.get("data", {}).get("customer")
        # request.state.customer_id = '1'
        if not request.state.customer_id:
            logger.warning(
                f"Missing customer_id in token payload: {request.method} {request.url}"
            )
            return JSONResponse(
                status_code=400, content="customer_id missing in token payload"
            )

        # Log incoming request
        request_body = await request.body()
        request_body_str = (
            request_body.decode("utf-8", errors="replace") if request_body else None
        )

        # logger.info(
        #     f"Request: {request.method} {request.url} | "
        #     f"Headers: {dict(request.headers)} | Body: {request_body_str}"
        # )
        try:
            # Process request and capture response
            response: Response | StreamingResponse | JSONResponse | PlainTextResponse = await call_next(
                request
            )
            process_time = time.time() - start_time

            if request.url.path in VERSION2_URLS:
                return response
            # Capture response body
            response_body = b"".join(
                [section async for section in response.body_iterator]
            )

            async def response_body_generator():
                yield response_body

            response.body_iterator = response_body_generator()
            if str(response.status_code).startswith("2"):
                logger.info(
                    f"Response: {request.method} {request.url} | "
                    f"Status: {response.status_code} | Time: {process_time:.3f}s | "
                    f"Body: {response_body.decode() if response_body else None}"
                )
            else:
                logger.error(
                    f"Response: {request.method} {request.url} | "
                    f"Status: {response.status_code} | Time: {process_time:.3f}s | "
                    f"Body: {response_body.decode() if response_body else None}"
                )

            return response

        except Exception as e:
            logger.error(f"Internal Server Error: {traceback.format_exc()}")
            logger.info("Sending alert mail")
            send_alert_mail(traceback.format_exc())
            return JSONResponse(status_code=500, content="Internal Server Error")


def ai_call_limit(func):
    @wraps(func)
    async def wrapper(request: Request, *args, **kwargs):
        # 1️⃣ Extract JWT Token
        auth_header = request.headers.get("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            return JSONResponse(
                status_code=401, content="Authorization header missing or invalid"
            )

        token = auth_header.split(" ")[1]
        try:
            payload = decode_jwt(token)
            data = payload.get("data", {})
            features = data.get("features")
            if features:
                settings.DEFAULT_AI_CALL_LIMIT = features.get(
                    "ai_messages", settings.DEFAULT_AI_CALL_LIMIT
                )
        except HTTPException as e:
            return JSONResponse(status_code=e.status_code, content={"detail": e.detail})
        except Exception as e:
            return JSONResponse(
                status_code=500, content={"detail": f"Internal server error: {str(e)}"}
            )

        # 2️⃣ Get user ID from JWT
        request.state.customer_id = payload.get("data", {}).get("customer")
        if not request.state.customer_id:
            return JSONResponse(
                status_code=400, content="customer_id missing in token payload"
            )

        account_id = request.state.customer_id
        max_calls = settings.DEFAULT_AI_CALL_LIMIT
        expiry_hours = settings.AI_CALL_RESET_HOUR  # Expiry duration from settings

        # 3️⃣ Calculate Reset Time (Every expiry_hours duration)
        now = datetime.now(timezone.utc)
        first_call_key = f"account:{account_id}:ai_calls:start_time"
        first_call_time = cache.redis_client.get(first_call_key)

        if first_call_time:
            first_call_time = datetime.fromisoformat(first_call_time.decode())
        else:
            first_call_time = now
            cache.redis_client.set(
                first_call_key, first_call_time.isoformat(), ex=expiry_hours * 3600
            )  # Expiry in seconds

        reset_time = first_call_time + timedelta(hours=expiry_hours)

        # 4️⃣ Redis Key for Rate Limiting (Unique per user per expiry period)
        redis_key = f"account:{account_id}:ai_calls"

        # 5️⃣ Retrieve Call Count Safely
        call_data = cache.redis_client.get(redis_key)
        call_count = int(call_data) if call_data and call_data.isdigit() else 0

        logger.info(f"User {account_id}: Call count = {call_count}, Max = {max_calls}")

        # 6️⃣ Enforce Limit: Reject if Over Limit
        if call_count >= max_calls:
            return JSONResponse(
                status_code=401,
                content={
                    "message": f"You have exceeded your usage limit. Your quota will reset after 30 days."
                },
            )

        # 7️⃣ Increase Count and Store in Redis
        cache.redis_client.set(
            redis_key, call_count + 1, ex=int((reset_time - now).total_seconds())
        )

        # 8️⃣ Allow the API Call to Proceed
        return await func(request, *args, **kwargs)

    return wrapper


def get_file_extension(filename: str) -> str:
    return os.path.splitext(filename)[-1].lower().strip(".")


async def validate_image_file(file: UploadFile):
    ext = get_file_extension(file.filename)
    if ext not in settings.ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400,
            detail=f"Only {', '.join(settings.ALLOWED_EXTENSIONS)} files are allowed",
        )
    return file


async def validate_knowledge(file: Optional[UploadFile] = None):
    if file is None or file.filename == "":
        return None  # ✅ Fix empty file issue
    return file


async def validate_link(link: Optional[str] = None):
    if link and not (link.startswith("http://") or link.startswith("https://")):
        raise HTTPException(
            status_code=400, detail="Link must start with http:// or https://"
        )
    return link


def get_custom_expiry(hours: int, minutes: int):
    """Calculate expiry time in seconds based on given hours and minutes."""
    return int(timedelta(hours=hours, minutes=minutes).total_seconds())


def upload_limit(file_type: str):
    """Decorator to enforce upload limits per file type."""

    def decorator(func):
        @wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            customer_id = request.state.customer_id
            if not customer_id:
                raise HTTPException(status_code=400, detail="Missing Customer ID")
            auth_header = request.headers.get("Authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return JSONResponse(
                    status_code=401, content="Authorization header missing or invalid"
                )

            token = auth_header.split(" ")[1]
            try:
                payload = decode_jwt(token)
                data = payload.get("data", {})
                features = data.get("features")
                if features:
                    settings.GENERIC_IMAGE_UPLOAD_LIMIT = features.get(
                        "ai_images", settings.GENERIC_IMAGE_UPLOAD_LIMIT
                    )
                    settings.SCANNED_IMAGE_UPLOAD_LIMIT = features.get(
                        "vue_lens", settings.SCANNED_IMAGE_UPLOAD_LIMIT
                    )
            except HTTPException as e:
                return JSONResponse(
                    status_code=e.status_code, content={"detail": e.detail}
                )
            except Exception as e:
                return JSONResponse(
                    status_code=500,
                    content={"detail": f"Internal server error: {str(e)}"},
                )

            limit = 10
            expiry_hours = settings.AI_CALL_RESET_HOUR
            key = "upload"

            if file_type == "generic_image":
                limit = settings.GENERIC_IMAGE_UPLOAD_LIMIT
                expiry_hours = settings.GENERIC_IMAGE_UPLOAD_EXPIRY_HOURS
                key = "generic_image"
            elif file_type == "scanned_image":
                limit = settings.SCANNED_IMAGE_UPLOAD_LIMIT
                expiry_hours = settings.SCANNED_IMAGE_UPLOAD_EXPIRY_HOURS
                key = "scanned_image"

            expiry_seconds = expiry_hours * 3600

            cache_key = f"{key}_upload_limit:{customer_id}:{file_type}:{datetime.now(timezone.utc).date()}"
            count = cache.redis_client.get(cache_key)
            count = int(count) if count else 0

            if count >= limit * 2:
                raise HTTPException(
                    status_code=429,
                    detail=f"Exceeded upload limit {limit} for {file_type}",
                )

            try:
                response = await func(request, *args, **kwargs)
            except HTTPException as e:
                raise e
            except Exception as e:
                raise HTTPException(status_code=500, detail=str(e))

            if count == 0:
                cache.redis_client.set(cache_key, 1, ex=expiry_seconds)
            else:
                cache.redis_client.incr(cache_key)

            return response

        return wrapper

    return decorator
