# app/config.py

from platform import node
from typing import List

from pydantic import Field
from pydantic_settings import BaseSettings

DEV_HOSTS = ["localhost", "127.0.0.1", "0.0.0.0", "narendra", "lokendra"]

ENV = node()


class Settings(BaseSettings):
    DATABASE_URL: str = Field(..., env="DATABASE_URL")
    ENVIRONMENT: str = Field(..., env="ENVIRONMENT")
    S3_BUCKET_NAME: str = Field(..., env="S3_BUCKET_NAME")
    AWS_ACCESS_KEY: str = Field(..., env="AWS_ACCESS_KEY")
    AWS_SECRET_KEY: str = Field(..., env="AWS_SECRET_KEY")
    AWS_REGION: str = Field(..., env="AWS_REGION")
    GROQ_API_KEY: str = Field(..., env="GROQ_API_KEY")
    OPENAI_API_KEY: str = Field(..., env="OPENAI_API_KEY")
    PERPLEXITY_KEY: str = Field(..., env="PERPLEXITY_KEY")
    MONGO_URL: str = Field(..., env="MONGO_URL")
    DATABASE_1: str = Field(..., env="DATABASE_1")
    DATABASE_5: str = Field(..., env="DATABASE_5")
    TRADES_DATABASE: str = Field(..., env="TRADES_DATABASE")
    S3_HOSTNAME: str = Field(..., env="S3_HOSTNAME")
    MODEL_NAME: str = Field(..., env="MODEL_NAME")
    PERPLEXITY_MODEL_NAME: str = Field(..., env="PERPLEXITY_MODEL_NAME")
    GPT_4O_MINI_MODEL_NAME: str = Field(..., env="GPT_4O_MINI_MODEL_NAME")
    gpt_4_1_mini: str = Field(..., env="gpt_4_1_mini")
    TMP_DIR: str = Field(..., env="TMP_DIR")
    # GOOGLE_CSE_ID: str = Field(..., env="GOOGLE_CSE_ID")
    # GOOGLE_API_KEY: str = Field(..., env="GOOGLE_API_KEY")
    ANTHROPIC_API_KEY: str = Field(..., env="ANTHROPIC_API_KEY")
    CLAUDE_MODEL_NAME: str = Field(..., env="CLAUDE_MODEL_NAME")
    CLAUDE_SONNET_MODEL_NAME: str = Field(..., env="CLAUDE_SONNET_MODEL_NAME")
    FMP_API_KEY: str = Field(..., env="FMP_API_KEY")
    FMP_URL: str = Field(..., env="FMP_URL")
    # SERP_API_KEY: str = Field(..., env="SERP_API_KEY")
    SEARCH_API_KEY: str = Field(..., env="SEARCH_API_KEY")
    ALLOWED_EXTENSIONS: set = {"png", "jpg", "jpeg"}
    ALLOWED_KNOWLEDGES: set = {"pdf", "csv", "txt", "docx", *ALLOWED_EXTENSIONS}
    GENERIC_IMAGE_UPLOAD_LIMIT: int = Field(..., env="GENERIC_IMAGE_UPLOAD_LIMIT")
    SCANNED_IMAGE_UPLOAD_EXPIRY_HOURS: int = Field(
        ..., env="SCANNED_IMAGE_UPLOAD_EXPIRY_HOURS"
    )
    # GENERIC_IMAGE_UPLOAD_EXPIRY_MINUTES: int = Field(
    #     ..., env="GENERIC_IMAGE_UPLOAD_EXPIRY_MINUTES"
    # )
    SCANNED_IMAGE_UPLOAD_LIMIT: int = Field(..., env="SCANNED_IMAGE_UPLOAD_LIMIT")
    GENERIC_IMAGE_UPLOAD_EXPIRY_HOURS: int = Field(
        ..., env="GENERIC_IMAGE_UPLOAD_EXPIRY_HOURS"
    )
    # SCANNED_IMAGE_UPLOAD_EXPIRY_MINUTES: int = Field(
    #     ..., env="SCANNED_IMAGE_UPLOAD_EXPIRY_MINUTES"
    # )
    DEFAULT_AI_CALL_LIMIT: int = Field(..., env="DEFAULT_AI_CALL_LIMIT")
    AI_CALL_RESET_HOUR: int = Field(..., env="AI_CALL_RESET_HOUR")
    ALLOWED_ORIGINS: List[str] = Field(..., env="ALLOWED_ORIGINS")
    AGENT_LIMIT: int = Field(..., env="AGENT_LIMIT")
    KNOWLEDGE_LIMIT: int = Field(..., env="KNOWLEDGE_LIMIT")
    MAIL_PASSWORD: str = Field(..., env="MAIL_PASSWORD")
    SENDING_GMAIL: str = Field(..., env="SENDING_GMAIL")
    ADMIN_MAILS: str = Field(..., env="ADMIN_MAILS")
    REDIS_HOST: str = Field("localhost", env="REDIS_HOST")
    REDIS_PORT: int = Field(..., env="REDIS_PORT")

    class Config:
        if ENV in DEV_HOSTS:
            print("using development settings")
            env_file = ".env.development"
        else:
            print("using test settings")
            env_file = ".env"
        extra = "ignore"


# Instance of settings that can be used throughout the application
settings = Settings()
