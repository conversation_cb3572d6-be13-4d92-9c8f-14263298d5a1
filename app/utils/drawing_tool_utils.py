from typing import Dict, Any, List, Optional, Tuple
import json
import re
import html
from openai import OpenAI
from app.core.config import settings
from app.utils.logger import generic_logger as logger

# Configuration
ANTHROPIC_KEY = settings.ANTHROPIC_API_KEY

# Indicator file mapping
INDICATOR_FILES = {
    # Basic Technical Indicators
    "rsi": "indicator_calculation/rsi.py",
    "relative_strength_index": "indicator_calculation/rsi.py",
    "macd": "indicator_calculation/macd.py",
    "moving_average_convergence_divergence": "indicator_calculation/macd.py",
    "ema": "indicator_calculation/ema.py",
    "exponential_moving_average": "indicator_calculation/ema.py",
    "sma": "indicator_calculation/sma.py",
    "simple_moving_average": "indicator_calculation/sma.py",
    "dema": "indicator_calculation/dema.py",
    "double_exponential_moving_average": "indicator_calculation/dema.py",
    "tema": "indicator_calculation/tema.py",
    "triple_exponential_moving_average": "indicator_calculation/tema.py",
    "trima": "indicator_calculation/trima.py",
    "triangular_moving_average": "indicator_calculation/trima.py",
    "rma": "indicator_calculation/rma.py",
    "rolling_moving_average": "indicator_calculation/rma.py",
    "vwma": "indicator_calculation/vwma.py",
    "volume_weighted_moving_average": "indicator_calculation/vwma.py",

    # Momentum Indicators
    "stoch": "indicator_calculation/stoch.py",
    "stochastic_oscillator": "indicator_calculation/stoch.py",
    "willr": "indicator_calculation/willr.py",
    "williams_r": "indicator_calculation/willr.py",
    "roc": "indicator_calculation/roc.py",
    "price_rate_of_change": "indicator_calculation/roc.py",
    "ppo": "indicator_calculation/ppo.py",
    "percentage_price_oscillator": "indicator_calculation/ppo.py",
    "ao": "indicator_calculation/ao.py",
    "awesome_oscillator": "indicator_calculation/ao.py",
    "cmo": "indicator_calculation/cmo.py",
    "chaikin_oscillator": "indicator_calculation/cmo.py",
    "apo": "indicator_calculation/apo.py",
    "absolute_price_oscillator": "indicator_calculation/apo.py",

    # Volatility Indicators
    "atr": "indicator_calculation/atr.py",
    "average_true_range": "indicator_calculation/atr.py",
    "bb": "indicator_calculation/bb.py",
    "bollinger_bands": "indicator_calculation/bb.py",
    "bbw": "indicator_calculation/bbw.py",
    "bollinger_band_width": "indicator_calculation/bbw.py",
    "kc": "indicator_calculation/kc.py",
    "keltner_channel": "indicator_calculation/kc.py",
    "dc": "indicator_calculation/dc.py",
    "donchian_channel": "indicator_calculation/dc.py",
    "tr": "indicator_calculation/tr.py",
    "true_range": "indicator_calculation/tr.py",
    "mstd": "indicator_calculation/mstd.py",
    "moving_standard_deviation": "indicator_calculation/mstd.py",
    "ab": "indicator_calculation/ab.py",
    "acceleration_bands": "indicator_calculation/ab.py",
    "ce": "indicator_calculation/ce.py",
    "chandelier_exit": "indicator_calculation/ce.py",
    "ulcer": "indicator_calculation/ulcer.py",
    "ulcer_index": "indicator_calculation/ulcer.py",

    # Volume Indicators
    "obv": "indicator_calculation/obv.py",
    "on_balance_volume": "indicator_calculation/obv.py",
    "ad": "indicator_calculation/ad.py",
    "accumulation_distribution": "indicator_calculation/ad.py",
    "cmf": "indicator_calculation/cmf.py",
    "chaikin_money_flow": "indicator_calculation/cmf.py",
    "emv": "indicator_calculation/emv.py",
    "ease_of_movement": "indicator_calculation/emv.py",
    "fi": "indicator_calculation/fi.py",
    "force_index": "indicator_calculation/fi.py",
    "mfi": "indicator_calculation/mfi.py",
    "money_flow_index": "indicator_calculation/mfi.py",
    "nvi": "indicator_calculation/nvi.py",
    "negative_volume_index": "indicator_calculation/nvi.py",
    "vpt": "indicator_calculation/vpt.py",
    "volume_price_trend": "indicator_calculation/vpt.py",
    "vwap": "indicator_calculation/vwap.py",
    "volume_weighted_average_price": "indicator_calculation/vwap.py",
    "pvo": "indicator_calculation/pvo.py",
    "percentage_volume_oscillator": "indicator_calculation/pvo.py",

    # Trend Indicators
    "aroon": "indicator_calculation/aroon.py",
    "aroon_indicator": "indicator_calculation/aroon.py",
    "bop": "indicator_calculation/bop.py",
    "balance_of_power": "indicator_calculation/bop.py",
    "cfo": "indicator_calculation/cfo.py",
    "chande_forecast_oscillator": "indicator_calculation/cfo.py",
    "cci": "indicator_calculation/cci.py",
    "community_channel_index": "indicator_calculation/cci.py",
    "commodity_channel_index": "indicator_calculation/cci.py",
    "mi": "indicator_calculation/mi.py",
    "mass_index": "indicator_calculation/mi.py",
    "psar": "indicator_calculation/psar.py",
    "parabolic_sar": "indicator_calculation/psar.py",
    "kdj": "indicator_calculation/kdj.py",
    "random_index": "indicator_calculation/kdj.py",
    "qstick": "indicator_calculation/qstick.py",
    "since": "indicator_calculation/since.py",
    "since_change": "indicator_calculation/since.py",
    "trix": "indicator_calculation/trix.py",
    "triple_exponential_average": "indicator_calculation/trix.py",
    "typprice": "indicator_calculation/typprice.py",
    "typical_price": "indicator_calculation/typprice.py",
    "vortex": "indicator_calculation/vortex.py",
    "vortex_indicator": "indicator_calculation/vortex.py",
    "ichicloud": "indicator_calculation/ichicloud.py",
    "ichimoku_cloud": "indicator_calculation/ichicloud.py",

    # Utility Indicators
    "msum": "indicator_calculation/msum.py",
    "moving_sum": "indicator_calculation/msum.py",
    "mmin": "indicator_calculation/mmin.py",
    "moving_min": "indicator_calculation/mmin.py",
    "mmax": "indicator_calculation/mmax.py",
    "moving_max": "indicator_calculation/mmax.py",
    "po": "indicator_calculation/po.py",
    "projection_oscillator": "indicator_calculation/po.py",

    # ICT/Smart Money Concepts
    "fvg": "indicator_calculation/fvg.py",
    "fair_value_gap": "indicator_calculation/fvg.py",
    "order_block": "indicator_calculation/order_block.py",
    "order block": "indicator_calculation/order_block.py",
    "smartmoneyconcepts": "indicator_calculation/smartmoneyconcepts.py",
    "smart_money_concepts": "indicator_calculation/smartmoneyconcepts.py",
    "orderblockdetector": "indicator_calculation/orderblockdetector.py",
    "order_block_detector": "indicator_calculation/orderblockdetector.py",
    "ictconcepts": "indicator_calculation/ictconcepts.py",
    "ict_concepts": "indicator_calculation/ictconcepts.py",
    "fvginstantaneousmitigationsignals": "indicator_calculation/fvginstantaneousmitigationsignals.py",
    "fvg_instantaneous_mitigation_signals": "indicator_calculation/fvginstantaneousmitigationsignals.py",
    "breakerblocks": "indicator_calculation/breakerblocks.py",
    "breaker_blocks": "indicator_calculation/breakerblocks.py",
    "ictimpliedfairvaluegap": "indicator_calculation/ictimpliedfairvaluegap.py",
    "ict_implied_fair_value_gap": "indicator_calculation/ictimpliedfairvaluegap.py",
    "ifvg": "indicator_calculation/ictimpliedfairvaluegap.py",
    "ictkillzones": "indicator_calculation/ictkillzones.py",
    "ict_killzones": "indicator_calculation/ictkillzones.py",
    "liquidityswings": "indicator_calculation/liquidityswings.py",
    "liquidity_swings": "indicator_calculation/liquidityswings.py",
    "dridr": "indicator_calculation/dridr.py",
    "dr_idr": "indicator_calculation/dridr.py",
    "daily_range": "indicator_calculation/dridr.py",
    "initial_daily_range": "indicator_calculation/dridr.py",

    # Pattern Recognition
    "candlebodysupportresistance": "indicator_calculation/candlebodysupportresistance.py",
    "candle_body_support_resistance": "indicator_calculation/candlebodysupportresistance.py",
    "reversalcandlestickstructure": "indicator_calculation/reversalcandlestickstructure.py",
    "reversal_candlestick_structure": "indicator_calculation/reversalcandlestickstructure.py",
    "tweezerkangarootail": "indicator_calculation/tweezerkangarootail.py",
    "tweezer_kangaroo_tail": "indicator_calculation/tweezerkangarootail.py",
    "purepriceactionstructures": "indicator_calculation/purepriceactionstructures.py",
    "pure_price_action_structures": "indicator_calculation/purepriceactionstructures.py",
    "'false'breakoutpattern": "indicator_calculation/falsebreakoutpattern.py",
    "false_breakout_pattern": "indicator_calculation/falsebreakoutpattern.py",

    # Breakout and Range Indicators
    "timelyopeningrangebreakout": "indicator_calculation/timelyopeningrangebreakout.py",
    "timely_opening_range_breakout": "indicator_calculation/timelyopeningrangebreakout.py",
    "torb": "indicator_calculation/timelyopeningrangebreakout.py",
    "openingrangewithtargetsandbreakouts": "indicator_calculation/openingrangewithtargetsandbreakouts.py",
    "opening_range_with_targets_and_breakouts": "indicator_calculation/openingrangewithtargetsandbreakouts.py",
    "algozone": "indicator_calculation/algozone.py",
    "algo_zone": "indicator_calculation/algozone.py",
    "algozone_structure": "indicator_calculation/algozone.py",

    # Fibonacci
    "fibonacci": "indicator_calculation/fibonacci.py",
    "fib": "indicator_calculation/fibonacci.py",

    # Trend Analysis
    "trend": "indicator_calculation/trend_analysis.py",
    "trend_analysis": "indicator_calculation/trend_analysis.py",
    "trend analysis": "indicator_calculation/trend_analysis.py",
    "moving_average": "indicator_calculation/trend_analysis.py",
    "ma": "indicator_calculation/trend_analysis.py",
    "support": "indicator_calculation/trend_analysis.py",
    "resistance": "indicator_calculation/trend_analysis.py",
    "support and resistance": "indicator_calculation/trend_analysis.py",
    "support_and_resistance": "indicator_calculation/trend_analysis.py",
}


def get_antropi_openai_client():
    return OpenAI(
        api_key=ANTHROPIC_KEY,
        base_url="https://api.anthropic.com/v1/"
    )


class TechnicalAnalysisAgent:
    """Simplified Technical Analysis Agent for metadata-based analysis"""

    def __init__(self, openai_client):
        self.client = openai_client
        self.analysis_steps = []

    def analyze_query(self, query: str) -> List[str]:
        """Analyze user query to identify required indicators"""
        logger.info(f"🔍 Analyzing query: '{query}'")

        indicator_prompt = f"""
                You are a technical analysis expert. Analyze this question and identify ONLY the essential indicators needed.

                Question: "{query}"

                Available indicators: RSI, MACD, EMA, SMA, DEMA, TEMA, TRIMA, RMA, VWMA, Stochastic, Williams %R, ROC, PPO, Awesome Oscillator, Chaikin Oscillator, APO, ATR, Bollinger Bands, Bollinger Band Width, Keltner Channel, Donchian Channel, True Range, Moving Standard Deviation, Acceleration Bands, Chandelier Exit, Ulcer Index, OBV, VWAP, Accumulation/Distribution, Chaikin Money Flow, Ease of Movement, Force Index, Money Flow Index, Negative Volume Index, Volume Price Trend, Percentage Volume Oscillator, Aroon, Balance of Power, Chande Forecast Oscillator, CCI, Mass Index, Parabolic SAR, KDJ, Qstick, Since Change, TRIX, Typical Price, Vortex Indicator, Ichimoku Cloud, Moving Sum, Moving Min, Moving Max, Projection Oscillator, FVG, Order Block, Smart Money Concepts, Order Block Detector, ICT Concepts, FVG Instantaneous Mitigation Signals, Breaker Blocks, ICT Implied Fair Value Gap, ICT Kill Zones, Liquidity Swings, DR/IDR Candles, Candle Body Support/Resistance, Reversal Candlestick Structure, Tweezer and Kangaroo Tail, Pure Price Action Structures, False Breakout Pattern, Timely Opening Range Breakout, Opening Range with Targets, AlgoZone Structure, Fibonacci, Trend Analysis

                STRICT MAPPING RULES (use ONLY what's explicitly mentioned or directly needed):

                **BASIC TECHNICAL INDICATORS:**
                1. "RSI" OR "relative strength index" OR "overbought" OR "oversold" OR "momentum oscillator" → "RSI"
                2. "MACD" OR "moving average convergence divergence" OR "signal line" OR "histogram" → "MACD"
                3. "EMA" OR "exponential moving average" OR "exponential MA" → "EMA"
                4. "SMA" OR "simple moving average" OR "simple MA" → "SMA"
                5. "moving average" OR "MA" OR "trend line" OR "average price" → "SMA, EMA"
                6. "bollinger bands" OR "BB" OR "upper band" OR "lower band" OR "squeeze" → "Bollinger Bands"
                7. "ATR" OR "average true range" OR "volatility" OR "true range" → "ATR"
                8. "stochastic" OR "stoch" OR "%K" OR "%D" → "Stochastic"
                9. "williams %R" OR "williams percent R" → "Williams %R"

                **VOLUME INDICATORS:**
                10. "OBV" OR "on balance volume" OR "volume flow" → "OBV"
                11. "VWAP" OR "volume weighted average price" OR "institutional benchmark" → "VWAP"
                12. "volume" OR "trading volume" OR "volume analysis" → "OBV, VWAP"
                13. "money flow" OR "MFI" OR "money flow index" → "Money Flow Index"
                14. "chaikin money flow" OR "CMF" → "Chaikin Money Flow"
                15. "accumulation" OR "distribution" OR "A/D line" → "Accumulation/Distribution"
                16. "force index" OR "buying force" OR "selling force" → "Force Index"

                **TREND INDICATORS:**
                17. "trend" OR "trend direction" OR "trend strength" → "Trend Analysis"
                18. "support and resistance" OR "support/resistance" OR "key levels" OR "S/R" → "Trend Analysis"
                19. "aroon" OR "aroon indicator" → "Aroon"
                20. "parabolic SAR" OR "PSAR" OR "stop and reverse" → "Parabolic SAR"
                21. "CCI" OR "commodity channel index" → "CCI"
                22. "ichimoku" OR "ichimoku cloud" OR "kumo" → "Ichimoku Cloud"
                23. "vortex" OR "vortex indicator" → "Vortex Indicator"

                **VOLATILITY INDICATORS:**
                24. "keltner channel" OR "KC" → "Keltner Channel"
                25. "donchian channel" OR "DC" OR "price channel" → "Donchian Channel"
                26. "bollinger band width" OR "BBW" OR "band width" → "Bollinger Band Width"
                27. "chandelier exit" OR "trailing stop" → "Chandelier Exit"
                28. "ulcer index" OR "downside risk" → "Ulcer Index"

                **ICT/SMART MONEY CONCEPTS:**
                29. "order block" OR "OB" OR "institutional footprint" OR "institutional orders" → "Order Block"
                30. "fair value gap" OR "FVG" OR "imbalance" OR "gap" → "FVG"
                31. "smart money" OR "institutional trading" OR "SMC" → "Smart Money Concepts"
                32. "ICT" OR "inner circle trader" OR "ICT concepts" → "ICT Concepts"
                33. "breaker block" OR "breaker" OR "polarity change" → "Breaker Blocks"
                34. "liquidity" OR "liquidity sweep" OR "stop hunt" → "Liquidity Swings"
                35. "kill zones" OR "trading sessions" OR "london session" OR "new york session" → "ICT Kill Zones"
                36. "daily range" OR "IDR" OR "initial daily range" → "DR/IDR Candles"

                **FIBONACCI:**
                37. "fibonacci" OR "fib" OR "retracement" OR "extension" OR "61.8%" OR "38.2%" → "Fibonacci"

                **PATTERN RECOGNITION:**
                38. "candlestick" OR "candle pattern" OR "doji" OR "hammer" OR "engulfing" → "Reversal Candlestick Structure"
                39. "tweezer" OR "kangaroo tail" OR "long wick" → "Tweezer and Kangaroo Tail"
                40. "breakout" OR "false breakout" OR "failed breakout" → "False Breakout Pattern"
                41. "opening range" OR "OR" OR "morning range" → "Timely Opening Range Breakout"

                **ADVANCED INDICATORS:**
                42. "TRIX" OR "triple exponential" → "TRIX"
                43. "mass index" OR "reversal indicator" → "Mass Index"
                44. "KDJ" OR "random index" → "KDJ"
                45. "rate of change" OR "ROC" OR "momentum" → "ROC"

                **MULTI-CONCEPT QUERIES:**
                - For comprehensive analysis requests: Include 3-5 most relevant indicators
                - For specific strategy requests: Focus on strategy-specific indicators
                - For market structure: "Trend Analysis, Order Block, FVG"
                - For momentum analysis: "RSI, MACD, Stochastic"
                - For volume analysis: "OBV, VWAP, Money Flow Index"

                DO NOT add extra indicators unless explicitly mentioned in the question.
                ALWAYS prioritize the most directly relevant indicators.
                For general analysis, limit to 3-5 core indicators maximum.

                Examples:
                - "draw support and resistance" → "Trend Analysis"
                - "show RSI levels" → "RSI"
                - "identify order blocks" → "Order Block"
                - "RSI with order blocks" → "RSI, Order Block"
                - "fibonacci and trend analysis" → "Fibonacci, Trend Analysis"
                - "volume analysis with VWAP" → "VWAP, OBV"
                - "smart money concepts analysis" → "Smart Money Concepts, Order Block, FVG"
                - "momentum and trend analysis" → "RSI, MACD, Trend Analysis"
                - "comprehensive technical analysis" → "RSI, MACD, Bollinger Bands, Trend Analysis, VWAP"

                Return ONLY the essential indicators as a comma-separated list:
                """

        try:
            response = self.client.chat.completions.create(
                model=settings.CLAUDE_SONNET_MODEL_NAME,
                messages=[{"role": "user", "content": indicator_prompt}]
            )

            indicators_text = response.choices[0].message.content.strip()
            indicators = [ind.strip().lower() for ind in indicators_text.split(',')]

            self.analysis_steps.append(f"Identified indicators: {', '.join(indicators)}")
            logger.info(f"🎯 Identified indicators: {indicators}")

            return indicators

        except Exception as e:
            logger.error(f"❌ Error analyzing query: {str(e)}")
            return []

    def load_calculation_knowledge(self, indicators: List[str]) -> Dict[str, str]:
        """Load calculation knowledge for indicators"""
        logger.info(f"📚 Loading knowledge for {len(indicators)} indicators")

        knowledge = {}
        loaded_files = []

        for indicator in indicators:
            if indicator in INDICATOR_FILES:
                file_path = INDICATOR_FILES[indicator]
                try:
                    with open(file_path, 'r') as f:
                        content = f.read()

                    # Extract knowledge based on common patterns
                    if 'rsi =' in content:
                        knowledge[indicator] = content.split('rsi =')[1].strip().strip('"""')
                    elif 'fvg =' in content:
                        knowledge[indicator] = content.split('fvg =')[1].strip().strip('"""')
                    elif 'order_block =' in content:
                        knowledge[indicator] = content.split('order_block =')[1].strip().strip('"""')
                    elif 'fib =' in content:
                        knowledge[indicator] = content.split('fib =')[1].strip().strip('"""')
                    elif 'trend_analysis =' in content:
                        knowledge[indicator] = content.split('trend_analysis =')[1].strip().strip('"""')
                    else:
                        knowledge[indicator] = content

                    loaded_files.append(file_path)

                except Exception as e:
                    logger.warning(f"⚠️ Could not load {file_path}: {str(e)}")
                    continue
            else:
                logger.warning(f"⚠️ No mapping found for indicator: {indicator}")

        self.analysis_steps.append(f"Loaded {len(loaded_files)} calculation files")
        logger.info(f"✅ Loaded knowledge for {len(knowledge)} indicators")

        return knowledge

    def perform_analysis(self, query: str, metadata: Dict[str, Any], indicators: List[str],
                         knowledge: Dict[str, str]) -> Dict[str, Any]:
        """Perform AI analysis with metadata"""
        logger.info(f"🧠 Starting analysis for query: '{query}'")

        # Convert metadata to the format expected by the analysis
        market_data = {
            "message": "Success",
            "data": metadata.get("ohlc_data", []),
            "metadata": {
                "symbol": metadata.get("symbol", ""),
                "timeframe": metadata.get("timeframe", ""),
                "total_candles": len(metadata.get("ohlc_data", []))
            }
        }

        analysis_prompt = f"""
        You are an expert technical analyst with access to calculation methodologies.

        USER QUESTION: "{query}"

        REQUIRED INDICATORS: {', '.join(indicators)}

        CALCULATION KNOWLEDGE:
        {json.dumps(knowledge, indent=2)}

        MARKET DATA:
        {json.dumps(market_data, indent=2)}

        INSTRUCTIONS:
        1. Apply the calculation methodologies to the OHLC data
        2. Perform actual calculations for each required indicator
        3. Provide comprehensive analysis answering the user's question
        4. Generate precise drawing instructions for chart visualization

        RESPONSE FORMAT:
        Provide your response in two parts:

        1. ANALYSIS EXPLANATION (in html format that i can direct inject innerHTML from frontend side)
        2. DRAWING INSTRUCTIONS (in JSON format)

        For drawing instructions, use this exact format:
        ```json
        {{
            "horizontal_lines": [
                {{
                    "price": 1.16200,
                    "color": "red",
                    "style": "solid",
                    "width": 2,
                    "label": "Resistance"
                }}
            ],
            "rectangles": [
                {{
                    "start_time": "2025-06-25T04:45:00.000Z",
                    "end_time": "2025-06-25T06:00:00.000Z",
                    "high": 1.16200,
                    "low": 1.16100,
                    "color": "green",
                    "opacity": 0.3,
                    "label": "Order Block"
                }}
            ],
            "trend_lines": [
                {{
                    "start_point": {{"datetime": "2025-06-25T04:45:00.000Z", "price": 1.16100}},
                    "end_point": {{"datetime": "2025-06-25T06:00:00.000Z", "price": 1.16150}},
                    "color": "blue",
                    "style": "solid",
                    "width": 2,
                    "label": "Trend Line"
                }}
            ],
            "annotations": [
                {{
                    "datetime": "2025-06-25T05:00:00.000Z",
                    "price": 1.16150,
                    "text": "RSI Signal",
                    "color": "orange",
                    "arrow": true,
                    "arrow_direction": "up"
                }}
            ],
            "indicators": [
                {{
                    "name": "RSI",
                    "data_points": [
                        {{"datetime": "2025-06-25T04:45:00.000Z", "value": 65.5}},
                        {{"datetime": "2025-06-25T05:00:00.000Z", "value": 72.3}}
                    ],
                    "color": "purple",
                    "width": 2,
                    "panel": "sub"
                }}
            ]
        }}
        ```

        Make sure all coordinates use actual datetime and price values from the provided OHLC data.
        """

        try:
            response = self.client.chat.completions.create(
                model=settings.CLAUDE_SONNET_MODEL_NAME,
                messages=[{"role": "user", "content": analysis_prompt}]
            )

            result = response.choices[0].message.content
            explanation, drawing_instructions = self.parse_analysis_response(result)

            self.analysis_steps.append("Completed AI analysis with calculations")
            logger.info(f"✅ Analysis completed successfully")

            return {
                "explanation": explanation,
                "drawing_instructions": drawing_instructions,
                "success": True,
                "indicators_used": indicators
            }

        except Exception as e:
            logger.error(f"❌ Error in analysis: {str(e)}")
            return {"explanation": "",
                   "drawing_instructions": {},
                   "success": True,
                   "indicators_used": indicators}

    def parse_analysis_response(self, response_text: str) -> Tuple[str, Dict]:
        """Parse AI response to separate explanation and drawing instructions"""
        # Find JSON blocks in the response
        json_pattern = r'```json\s*(\{.*?\})\s*```'
        json_matches = re.findall(json_pattern, response_text, re.DOTALL)

        # Combine all JSON objects found
        drawing_instructions = {}
        for json_str in json_matches:
            try:
                json_obj = json.loads(json_str)
                for key, value in json_obj.items():
                    if key in drawing_instructions:
                        if isinstance(drawing_instructions[key], list) and isinstance(value, list):
                            drawing_instructions[key].extend(value)
                        else:
                            drawing_instructions[key] = value
                    else:
                        drawing_instructions[key] = value
            except json.JSONDecodeError:
                continue

        # Remove JSON blocks from the explanation
        explanation = response_text
        for match in re.finditer(json_pattern, response_text, re.DOTALL):
            explanation = explanation.replace(match.group(0), "")

        # Clean up the explanation text
        # explanation = re.sub(r'<[^>]+>', '', explanation)  # Remove HTML tags
        # explanation = html.unescape(explanation)  # Decode HTML entities
        explanation = re.sub(r'\n{3,}', '\n\n', explanation)  # Fix multiple newlines
        explanation = explanation.replace("DRAWING INSTRUCTIONS:", "").replace("DRAWING INSTRUCTIONS", "").replace("ANALYSIS EXPLANATION:", "").replace("ANALYSIS EXPLANATION", "")
        explanation = explanation.replace("\n", "").replace("#", "").strip()

        return explanation, drawing_instructions
